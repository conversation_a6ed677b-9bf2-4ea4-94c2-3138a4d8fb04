<script lang="ts">
  import { goto } from '$app/navigation';
  import { page } from '$app/stores';
  import { challengeStore } from '$lib/store';
  import { tick } from 'svelte';
  import ThemeToggle from './ThemeToggle.svelte';

  let showResetModal = false;
  let modalElement: HTMLDivElement;
  let cancelButton: HTMLButtonElement;
  let confirmButton: HTMLButtonElement;
  let previouslyFocusedElement: HTMLElement | null = null;

  function goHome() {
    goto('/home');
  }


  // Focus trap utility functions
  function getFocusableElements(): HTMLElement[] {
    if (!modalElement) return [];

    const focusableSelectors = [
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      'a[href]',
      '[tabindex]:not([tabindex="-1"])'
    ].join(', ');

    return Array.from(modalElement.querySelectorAll(focusableSelectors)) as HTMLElement[];
  }

  function trapFocus(event: KeyboardEvent) {
    if (!showResetModal || event.key !== 'Tab') return;

    const focusableElements = getFocusableElements();
    if (focusableElements.length === 0) return;

    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    if (event.shiftKey) {
      // Shift + Tab: moving backwards
      if (document.activeElement === firstElement) {
        event.preventDefault();
        lastElement.focus();
      }
    } else {
      // Tab: moving forwards
      if (document.activeElement === lastElement) {
        event.preventDefault();
        firstElement.focus();
      }
    }
  }

  async function openResetModal() {
    // Store the currently focused element to restore later
    previouslyFocusedElement = document.activeElement as HTMLElement;

    showResetModal = true;

    // Focus the first focusable element after rendering
    await tick();
    const focusableElements = getFocusableElements();
    if (focusableElements.length > 0) {
      focusableElements[0].focus();
    }
  }

  function closeResetModal() {
    showResetModal = false;

    // Restore focus to the previously focused element
    if (previouslyFocusedElement) {
      previouslyFocusedElement.focus();
      previouslyFocusedElement = null;
    }
  }

  // Additional safety: if focus somehow escapes the modal, bring it back
  function handleFocusEscape(event: FocusEvent) {
    if (!showResetModal || !modalElement) return;

    const target = event.target as HTMLElement;
    if (!modalElement.contains(target)) {
      event.preventDefault();
      const focusableElements = getFocusableElements();
      if (focusableElements.length > 0) {
        focusableElements[0].focus();
      }
    }
  }

  function confirmResetProgress() {
    challengeStore.resetProgress();
    showResetModal = false;
    // Navigate to main menu after reset
    goto('/learn');
  }

  // Handle keyboard events for modal
  function handleKeydown(event: KeyboardEvent) {
    if (!showResetModal) return;

    if (event.key === 'Escape') {
      closeResetModal();
    } else if (event.key === 'Tab') {
      trapFocus(event);
    }
  }

  // Determine if we should show the header based on current route
  $: showHeader = $page.url.pathname !== '/home';
  $: showResetButton = $page.url.pathname === '/learn';
</script>

{#if showHeader}
  <header class="app-header">
    <div class="header-content">
      <div class="brand-section">
        <h1 class="brand-title">ByteCrafted</h1>
        <span class="brand-subtitle">Computer Architecture Learning Platform</span>
      </div>
      
      <nav class="header-nav">
        <button class="nav-button home-button" on:click={goHome}>
          <span class="nav-icon">🏠</span>
          Home
        </button>
        {#if showResetButton}
          <button class="nav-button reset-button" on:click={openResetModal}>
            <span class="nav-icon">🔄</span>
            Reset Progress
          </button>
        {/if}
        <ThemeToggle />
      </nav>
    </div>
  </header>
{/if}

<!-- Window event listeners for keyboard navigation and focus management -->
<svelte:window on:keydown={handleKeydown} on:focusin={handleFocusEscape} />

<!-- Custom Reset Progress Modal -->
{#if showResetModal}
   
  <div class="modal-backdrop" on:click={closeResetModal} on:keydown={handleKeydown}>
     
    <div class="reset-modal" bind:this={modalElement} on:click|stopPropagation on:keydown={handleKeydown} role="dialog" aria-labelledby="reset-modal-title" aria-describedby="reset-modal-description" aria-modal="true" tabindex="-1">
      <div class="modal-header">
        <div class="warning-icon">⚠️</div>
        <h2 id="reset-modal-title">Reset All Progress</h2>
      </div>

      <div class="modal-content">
        <p id="reset-modal-description" class="warning-message">
          <strong>This action cannot be undone!</strong><br>
          You are about to permanently delete all your learning progress.
        </p>

        <div class="reset-details">
          <h3>What will be reset:</h3>
          <ul>
            <li>✗ All completed challenges</li>
            <li>✗ All unlocked tools and components</li>
            <li>✗ Current level and module progress</li>
            <li>✗ Any saved circuit designs</li>
          </ul>
        </div>

        <div class="consequence-warning">
          <p>You will return to the very beginning of your ByteCrafted journey with only basic tools available.</p>
        </div>
      </div>

      <div class="modal-actions">
        <button class="cancel-button" bind:this={cancelButton} on:click={closeResetModal}>
          Cancel
        </button>
        <button class="confirm-reset-button" bind:this={confirmButton} on:click={confirmResetProgress}>
          <span class="danger-icon">🗑️</span>
          Reset All Progress
        </button>
      </div>
    </div>
  </div>
{/if}

<style>
  .app-header {
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-default);
    padding: var(--spacing-3) var(--spacing-6);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
    box-shadow: var(--shadow-md);
    backdrop-filter: blur(8px);
  }

  .header-content {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .brand-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
  }

  .brand-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    background: linear-gradient(135deg, var(--color-accent), var(--text-purple), var(--color-success));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    line-height: var(--line-height-tight);
  }

  .brand-subtitle {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-normal);
  }

  .header-nav {
    display: flex;
    gap: var(--spacing-3);
  }

  .nav-button {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    background-color: var(--bg-elevated);
    color: var(--text-primary);
    border: 1px solid var(--border-default);
    padding: var(--spacing-2) var(--spacing-4);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    text-decoration: none;
    white-space: nowrap;
  }

  .nav-button:hover {
    background-color: var(--bg-surface);
    border-color: var(--color-accent);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
    color: var(--text-primary);
  }

  .nav-button:focus-visible {
    outline: 2px solid var(--color-accent);
    outline-offset: 2px;
  }

  .nav-icon {
    font-size: var(--font-size-base);
  }

  .home-button:hover {
    border-color: var(--color-warning);
  }

  .app-button:hover {
    border-color: var(--text-success);
  }

  .reset-button:hover {
    border-color: var(--text-error);
    background-color: rgba(224, 108, 117, 0.1);
  }

  /* Reset Progress Modal Styles */
  .modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    backdrop-filter: blur(4px);
    animation: fadeIn 0.3s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  .reset-modal {
    background: linear-gradient(135deg, #0d1117 0%, #161b22 100%);
    border: 2px solid var(--text-error);
    border-radius: 16px;
    padding: 0;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.6);
    animation: modalSlideIn 0.3s ease-out;
    outline: none;
  }

  @keyframes modalSlideIn {
    from {
      opacity: 0;
      transform: translateY(-20px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  .modal-header {
    background: linear-gradient(135deg, var(--text-error) 0%, var(--text-warning) 100%);
    padding: 2rem;
    border-radius: 14px 14px 0 0;
    text-align: center;
    color: #000;
  }

  .warning-icon {
    font-size: 3rem;
    margin-bottom: 0.5rem;
    animation: pulse 1.5s infinite;
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.1);
    }
    100% {
      transform: scale(1);
    }
  }

  .modal-header h2 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: bold;
    color: #000;
  }

  .modal-content {
    padding: 2rem;
  }

  .warning-message {
    color: var(--text-error);
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    text-align: center;
    line-height: 1.5;
  }

  .warning-message strong {
    color: var(--text-warning);
    font-size: 1.2rem;
  }

  .reset-details {
    background-color: #161b22;
    border: 1px solid var(--text-error);
    border-radius: 8px;
    padding: 1.5rem;
    margin: 1.5rem 0;
  }

  .reset-details h3 {
    color: var(--text-warning);
    margin: 0 0 1rem 0;
    font-size: 1.1rem;
  }

  .reset-details ul {
    margin: 0;
    padding-left: 1.5rem;
    color: var(--text-primary);
    line-height: 1.6;
  }

  .reset-details li {
    margin-bottom: 0.5rem;
    color: var(--text-error);
  }

  .consequence-warning {
    background-color: rgba(224, 108, 117, 0.1);
    border: 1px solid var(--text-error);
    border-radius: 6px;
    padding: 1rem;
    margin-top: 1.5rem;
  }

  .consequence-warning p {
    margin: 0;
    color: var(--text-primary);
    font-style: italic;
    text-align: center;
    line-height: 1.4;
  }

  .modal-actions {
    padding: 0 2rem 2rem 2rem;
    display: flex;
    gap: 1rem;
    justify-content: center;
  }

  .cancel-button {
    background-color: var(--bg-elevated);
    color: var(--text-primary);
    border: 1px solid var(--border-light);
    padding: 1rem 2rem;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
  }

  .cancel-button:hover {
    background-color: var(--bg-tertiary);
    border-color: var(--text-accent);
    transform: translateY(-1px);
  }

  .confirm-reset-button {
    background: linear-gradient(135deg, var(--text-error) 0%, #c53030 100%);
    color: #fff;
    border: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 180px;
    justify-content: center;
  }

  .confirm-reset-button:hover {
    background: linear-gradient(135deg, #c53030 0%, #9b2c2c 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(224, 108, 117, 0.4);
  }

  .danger-icon {
    font-size: 1.1rem;
  }

  /* Responsive Design for Modal */
  @media (max-width: 768px) {
    .reset-modal {
      margin: 1rem;
      width: calc(100% - 2rem);
    }

    .modal-header {
      padding: 1.5rem;
    }

    .modal-header h2 {
      font-size: 1.5rem;
    }

    .modal-content {
      padding: 1.5rem;
    }

    .modal-actions {
      padding: 0 1.5rem 1.5rem 1.5rem;
      flex-direction: column;
    }

    .cancel-button,
    .confirm-reset-button {
      width: 100%;
      min-width: unset;
    }
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .app-header {
      padding: var(--spacing-sm) var(--spacing-md);
    }

    .header-content {
      flex-direction: column;
      gap: var(--spacing-md);
      align-items: stretch;
    }

    .brand-section {
      text-align: center;
    }

    .brand-title {
      font-size: 1.25rem;
    }

    .brand-subtitle {
      font-size: 0.75rem;
    }

    .header-nav {
      justify-content: center;
    }

    .nav-button {
      flex: 1;
      justify-content: center;
    }
  }
</style>
